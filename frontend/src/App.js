import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ReactMarkdown from 'react-markdown';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { RefreshCw, Activity, TrendingUp, TrendingDown, Minus, ExternalLink, BarChart3 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './components/ui/card';
import { Button } from './components/ui/button';
import { Badge } from './components/ui/badge';
import './App.css';

const API_BASE_URL = process.env.REACT_APP_BACKEND_URL;

// Circular Progress Component for Probability Gauges
const CircularGauge = ({ value, maxValue = 10, color, label }) => {
  const percentage = (value / maxValue) * 100;
  const radius = 45;
  const strokeWidth = 8;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDasharray = `${percentage * circumference / 100} ${circumference}`;

  return (
    <div className="flex flex-col items-center">
      <div className="relative">
        <svg
          height={radius * 2}
          width={radius * 2}
          className="transform -rotate-90"
        >
          <circle
            stroke="#e5e7eb"
            fill="transparent"
            strokeWidth={strokeWidth}
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
          <circle
            stroke={color}
            fill="transparent"
            strokeWidth={strokeWidth}
            strokeDasharray={strokeDasharray}
            strokeLinecap="round"
            r={normalizedRadius}
            cx={radius}
            cy={radius}
            className="transition-all duration-700 ease-in-out"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-2xl font-bold" style={{ color }}>
            {value}/10
          </span>
        </div>
      </div>
      <div className="mt-2 text-center">
        <div className="text-sm font-medium" style={{ color }}>
          {(value * 10).toFixed(0)}% Probability
        </div>
      </div>
    </div>
  );
};

// Horizontal Sentiment Gauge Component
const SentimentGauge = ({ sentiment, confidence, bullishScore, bearishScore }) => {
  const getSentimentScore = () => {
    if (!bullishScore || !bearishScore) return 0;
    
    // Calculate net sentiment score based on the difference between bullish and bearish scores
    // Convert from 0-10 scale to -100 to +100 scale
    const netScore = (bullishScore - bearishScore) * 10;
    
    // Clamp the score between -100 and 100
    return Math.max(-100, Math.min(100, netScore));
  };

  const score = getSentimentScore();
  const position = ((score + 100) / 200) * 100; // Convert -100 to 100 range to 0-100%

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-red-600">Bearish (-100)</span>
        <span className="text-sm font-medium text-gray-600">Neutral (0)</span>
        <span className="text-sm font-medium text-green-600">Bullish (+100)</span>
      </div>
      <div className="relative h-6 bg-gray-200 rounded-full">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-full opacity-30"></div>
        
        {/* Sentiment indicator */}
        <div 
          className="absolute top-0 h-6 w-8 bg-orange-500 rounded-full flex items-center justify-center transform -translate-x-4 transition-all duration-700"
          style={{ left: `${position}%` }}
        >
          <div className="w-2 h-2 bg-white rounded-full"></div>
        </div>
      </div>
      <div className="mt-2 flex items-center justify-center">
        <span className="text-lg font-bold text-orange-600">{score}</span>
        <span className="ml-2 text-sm text-gray-600">
          {sentiment === 'neutral' ? 'Neutral' : sentiment === 'bullish' ? 'Bullish' : 'Bearish'}
        </span>
      </div>
    </div>
  );
};

// AI Analysis Component
const AIAnalysisDisplay = ({ analysis }) => {
  if (!analysis) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>No analysis available</p>
        <p className="text-sm mt-1">Click "Refresh" to get latest AI analysis</p>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-900 mb-2">📊 Quick Summary</h4>
        <div className="prose prose-sm max-w-none prose-p:text-blue-800 prose-strong:text-blue-900 prose-li:text-blue-800">
          <ReactMarkdown
            components={{
              p: ({ children }) => <p className="text-blue-800 text-sm leading-relaxed mb-2">{children}</p>,
              strong: ({ children }) => <strong className="font-semibold text-blue-900">{children}</strong>,
              ul: ({ children }) => <ul className="list-disc pl-4 mb-2 space-y-1">{children}</ul>,
              li: ({ children }) => <li className="text-blue-800 text-sm">{children}</li>,
              em: ({ children }) => <em className="italic text-blue-700">{children}</em>,
            }}
          >
            {analysis}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

// Detailed Analysis Component
const DetailedAnalysisDisplay = ({ detailedAnalysis }) => {
  if (!detailedAnalysis) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>No detailed analysis available</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-strong:text-gray-900 prose-p:text-gray-700 prose-li:text-gray-700">
        <ReactMarkdown
          components={{
            // Custom styling for different markdown elements
            h1: ({ children }) => <h1 className="text-xl font-bold text-gray-800 mb-3">{children}</h1>,
            h2: ({ children }) => <h2 className="text-lg font-semibold text-gray-800 mb-2">{children}</h2>,
            h3: ({ children }) => <h3 className="text-base font-semibold text-gray-800 mb-2">{children}</h3>,
            p: ({ children }) => <p className="text-gray-700 mb-3 leading-relaxed">{children}</p>,
            strong: ({ children }) => <strong className="font-semibold text-gray-900">{children}</strong>,
            ul: ({ children }) => <ul className="list-disc pl-6 mb-3 space-y-1">{children}</ul>,
            ol: ({ children }) => <ol className="list-decimal pl-6 mb-3 space-y-1">{children}</ol>,
            li: ({ children }) => <li className="text-gray-700">{children}</li>,
            em: ({ children }) => <em className="italic text-gray-600">{children}</em>,
          }}
        >
          {detailedAnalysis}
        </ReactMarkdown>
      </div>
    </div>
  );
};

// Influential News Component
const InfluentialNewsDisplay = ({ influentialNews }) => {
  if (!influentialNews || influentialNews.length === 0) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>No news impact data available</p>
        <p className="text-sm mt-1">Click "Refresh" to get latest news analysis</p>
      </div>
    );
  }

  const getSentimentIcon = (direction) => {
    switch (direction) {
      case 'bullish':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'bearish':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  const getSentimentColor = (direction) => {
    switch (direction) {
      case 'bullish':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'bearish':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRelevanceBadgeColor = (relevance) => {
    switch (relevance) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-4 space-y-4">
      {influentialNews.map((news, index) => (
        <div
          key={index}
          className={`border rounded-lg p-4 transition-all hover:shadow-sm ${getSentimentColor(news.sentiment_direction)}`}
        >
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center space-x-2">
              {getSentimentIcon(news.sentiment_direction)}
              <Badge className={`text-xs ${getRelevanceBadgeColor(news.relevance)}`}>
                {news.relevance} relevance
              </Badge>
            </div>
            <div className="text-right">
              <div className="text-sm font-semibold text-gray-700">
                Impact: {news.impact_score}/10
              </div>
              <div className="text-xs text-gray-500">
                {news.source}
              </div>
            </div>
          </div>
          
          <h4 className="font-medium text-gray-900 leading-relaxed mb-2">
            {news.headline}
          </h4>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className={`text-xs font-medium px-2 py-1 rounded-full ${getSentimentColor(news.sentiment_direction)}`}>
                {news.sentiment_direction.charAt(0).toUpperCase() + news.sentiment_direction.slice(1)}
              </span>
            </div>
            
            <div className="w-full max-w-24 bg-gray-200 rounded-full h-1.5 mx-3">
              <div
                className={`h-1.5 rounded-full transition-all duration-500 ${
                  news.sentiment_direction === 'bullish' ? 'bg-green-500' :
                  news.sentiment_direction === 'bearish' ? 'bg-red-500' : 'bg-gray-500'
                }`}
                style={{ width: `${(news.impact_score / 10) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Market Drivers Component
const MarketDriversDisplay = ({ marketDrivers }) => {
  if (!marketDrivers || marketDrivers.length === 0) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>No market drivers data available</p>
        <p className="text-sm mt-1">Click "Refresh" to get latest analysis</p>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-3">
      <div className="flex items-center space-x-2 mb-4">
        <BarChart3 className="w-5 h-5 text-blue-600" />
        <h4 className="font-semibold text-gray-900">Key Market Drivers</h4>
      </div>
      
      <div className="space-y-3">
        {marketDrivers.map((driver, index) => (
          <div
            key={index}
            className="flex items-center space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
              {index + 1}
            </div>
            <div className="flex-1">
              <p className="text-gray-800 font-medium leading-relaxed">
                {driver}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <p className="text-xs text-gray-600 text-center">
          These factors are currently driving market sentiment based on AI analysis
        </p>
      </div>
    </div>
  );
};

function App() {
  const [sentimentData, setSentimentData] = useState(null);
  const [historyData, setHistoryData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [error, setError] = useState(null);

  const fetchSentimentAnalysis = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/analyze`);
      setSentimentData(response.data.sentiment_analysis);
      setLastUpdated(new Date());

      // Fetch updated history
      await fetchHistoryData();
    } catch (error) {
      console.error('Error fetching sentiment analysis:', error);
      setError(error.response?.data?.detail || 'Failed to fetch sentiment analysis');
    } finally {
      setLoading(false);
    }
  };

  const fetchHistoryData = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/sentiment-history`);
      const analyses = response.data.analyses;
      
      // Transform data for 24h trend chart
      const transformedData = analyses.map((analysis, index) => ({
        time: new Date(analysis.timestamp).getHours(),
        sentiment: analysis.overall_sentiment === 'bullish' ? analysis.bullish_score * 10 :
                  analysis.overall_sentiment === 'bearish' ? -analysis.bearish_score * 10 : 0,
        timestamp: analysis.timestamp
      }));
      
      setHistoryData(transformedData);
    } catch (error) {
      console.error('Error fetching history data:', error);
    }
  };

  useEffect(() => {
    fetchHistoryData();
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Market Sentiment Analysis</h1>
            <p className="text-gray-600">AI-powered real-time market sentiment analysis</p>
          </div>
        </div>

        {/* Status Bar */}
        <div className="flex items-center justify-between bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-green-600">AI Analysis</span>
            </div>
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                Last update: {lastUpdated ? formatTime(lastUpdated) : '--:-- --'}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">Model: Gemini 2.5 Pro</span>
            </div>
          </div>
          <Button
            onClick={fetchSentimentAnalysis}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
          >
            {loading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Main Sentiment Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

          {/* Overall Sentiment with Sentiment Scores */}
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold">Overall Sentiment</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Current market sentiment score (Confidence: {sentimentData ? (sentimentData.confidence_score * 100).toFixed(0) : 0}%)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {sentimentData ? (
                <>
                  <SentimentGauge
                    sentiment={sentimentData.overall_sentiment}
                    confidence={sentimentData.confidence_score * 100}
                    bullishScore={sentimentData.bullish_score}
                    bearishScore={sentimentData.bearish_score}
                  />
                  
                  {/* Sentiment Score Bars */}
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm font-medium">Bullish</span>
                        </div>
                        <span className="text-sm font-bold text-green-600">
                          {sentimentData.bullish_score.toFixed(1)}/10
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${(sentimentData.bullish_score / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <span className="text-sm font-medium">Bearish</span>
                        </div>
                        <span className="text-sm font-bold text-red-600">
                          {sentimentData.bearish_score.toFixed(1)}/10
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-red-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${(sentimentData.bearish_score / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="h-20 bg-gray-100 rounded-lg animate-pulse"></div>
              )}
            </CardContent>
          </Card>

          {/* AI Analysis Summary */}
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold">AI Analysis Summary</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Key insights analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-64 overflow-y-auto">
                <AIAnalysisDisplay analysis={sentimentData?.analysis_summary} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          
          {/* Sentiment Trend Chart */}
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-bold">Sentiment Trend (24h)</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Historical sentiment analysis powered by AI
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={historyData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="time" 
                      tickFormatter={(hour) => `${hour}:00`}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <YAxis 
                      domain={[-100, 100]}
                      tickFormatter={(value) => value.toString()}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <Tooltip 
                      labelFormatter={(hour) => `${hour}:00`}
                      formatter={(value) => [value, 'Sentiment Score']}
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="sentiment" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Market Drivers */}
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-bold">Market Drivers</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Key factors influencing current sentiment
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-64 overflow-y-auto">
                <MarketDriversDisplay marketDrivers={sentimentData?.market_drivers} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* News Impact Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          
          {/* Influential News */}
          <Card className="bg-white shadow-sm lg:col-span-1">
            <CardHeader>
              <CardTitle className="text-lg font-bold flex items-center space-x-2">
                <ExternalLink className="w-5 h-5 text-blue-600" />
                <span>Influential News</span>
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                News headlines that influenced AI sentiment analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-80 overflow-y-auto">
                <InfluentialNewsDisplay influentialNews={sentimentData?.influential_news} />
              </div>
            </CardContent>
          </Card>

          {/* Detailed AI Analysis */}
          <Card className="bg-white shadow-sm lg:col-span-1">
            <CardHeader>
              <CardTitle className="text-lg font-bold">Detailed Market Analysis</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Comprehensive analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-80 overflow-y-auto">
                <DetailedAnalysisDisplay detailedAnalysis={sentimentData?.detailed_analysis} />
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    </div>
  );
}

export default App;