version: '3.8'

services:

  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: market-sentiment-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - MONGO_URL=${MONGO_URL}
      - DB_NAME=${DB_NAME}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    networks:
      - app-network
    volumes:
      - ./backend:/app
    command: uvicorn server:app --host 0.0.0.0 --port 8000 --reload

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: market-sentiment-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - app-network
    environment:
      - REACT_APP_API_URL=http://localhost:8000



networks:
  app-network:
    driver: bridge
