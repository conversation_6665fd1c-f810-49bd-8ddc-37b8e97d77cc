version: '3.8'

services:
  # MongoDB for local development
  mongodb:
    image: mongo:7.0
    container_name: market-sentiment-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: market_sentiment
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network

  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: market-sentiment-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - MONGO_URL=***************************************************************************
      - DB_NAME=market_sentiment
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    depends_on:
      - mongodb
    networks:
      - app-network
    volumes:
      - ./backend:/app
    command: uvicorn server:app --host 0.0.0.0 --port 8000 --reload

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: market-sentiment-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - app-network
    environment:
      - REACT_APP_API_URL=http://localhost:8000

volumes:
  mongodb_data:

networks:
  app-network:
    driver: bridge
