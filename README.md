# Forex Market Sentiment Analysis App

A real-time forex market sentiment analysis application powered by Google Gemini 2.5 Pro. The app provides AI-driven market sentiment analysis, probability scores, and comprehensive market insights to help traders make informed decisions.

## 🚀 Features

- **AI-Powered Sentiment Analysis**: Uses Google Gemini 2.5 Pro with real-time data access for comprehensive market analysis
- **Real-time Market Insights**: Direct AI analysis without dependency on external news APIs
- **Interactive Dashboard**: Modern React frontend with real-time charts and gauges
- **Sentiment Scoring**: Provides bullish/bearish probability scores (0-10 scale)
- **Historical Trends**: 24-hour sentiment trend visualization
- **Detailed Analysis**: Comprehensive AI-generated market analysis and insights
- **Flexible Storage**: MongoDB with automatic fallback to in-memory storage

## 🏗️ Architecture

### Backend (FastAPI)
- **Framework**: FastAPI with async support
- **Database**: MongoDB with Motor (async driver)
- **AI Integration**: Google Gemini 2.5 Pro via google-generativeai
- **Features**: CORS enabled, real-time AI sentiment analysis, RESTful API

### Frontend (React)
- **Framework**: React 19 with modern hooks
- **UI Components**: Radix UI components with Tailwind CSS
- **Charts**: Recharts for data visualization
- **Build Tool**: CRACO (Create React App Configuration Override)
- **Styling**: Tailwind CSS with custom components

## 📋 Prerequisites

Before running the application, ensure you have:

- **Node.js** (v16 or higher)
- **Python** (v3.8 or higher)
- **MongoDB** (local installation or MongoDB Atlas)
- **Yarn** package manager
- **API Key** for:
  - Google Gemini API

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd market-sentiment
```

### 2. Backend Setup

#### Install Python Dependencies
```bash
cd backend
pip install -r requirements.txt
```

#### Configure Environment Variables
The backend uses a `.env` file for configuration. Create the environment file from the example:

```bash
cd backend
cp .env.example .env
```

Then edit `backend/.env` with your actual values:

```env
MONGO_URL="mongodb://localhost:27017"
DB_NAME="forex_sentiment_db"

# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY="your_actual_gemini_api_key_here"
```

#### Start MongoDB (Optional)
MongoDB is optional - the app will automatically fall back to in-memory storage if MongoDB is not available:
```bash
# On macOS with Homebrew
brew services start mongodb-community

# On Ubuntu/Debian
sudo systemctl start mongod

# Or use MongoDB Atlas (cloud) by updating MONGO_URL in .env
```

#### Run the Backend Server
```bash
cd backend
python -m uvicorn server:app --reload --host 0.0.0.0 --port 8000
```

The backend API will be available at `http://localhost:8000`

### 3. Frontend Setup

#### Install Dependencies
```bash
cd frontend
yarn install
```

#### Configure Environment Variables
Create the frontend environment file from the example:

```bash
cd frontend
cp .env.example .env
```

Then edit `frontend/.env` if needed (default values should work for local development):

```env
REACT_APP_BACKEND_URL=http://localhost:8000
```

#### Start the Frontend Development Server
```bash
cd frontend
yarn start
```

The frontend will be available at `http://localhost:3000`

## 🔑 API Key Setup

### Google Gemini API
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to `backend/.env`

## 📊 API Endpoints

### Core Endpoints
- `GET /api/` - API status check
- `POST /api/analyze` - Trigger new AI sentiment analysis
- `GET /api/sentiment-history` - Get historical sentiment data
- `GET /api/status` - Check API connectivity status

### Example API Response
```json
{
  "sentiment_analysis": {
    "overall_sentiment": "bullish",
    "bullish_score": 7.5,
    "bearish_score": 2.5,
    "confidence_score": 0.85,
    "analysis_summary": "Strong positive sentiment driven by favorable economic indicators...",
    "detailed_analysis": "Comprehensive market analysis covering major currency pairs, economic events, and outlook..."
  }
}
```

## 🎯 Usage

1. **Start the Application**: Follow the installation steps above
2. **Refresh Data**: Click the "Refresh" button to get latest AI analysis
3. **View Sentiment**: Monitor the overall sentiment gauge and probability scores
4. **Track Trends**: Use the 24-hour trend chart to see sentiment changes over time
5. **Read Analysis**: Review the AI-generated summary and detailed market analysis

## 🛠️ Development

### Project Structure
```
market-sentiment/
├── backend/
│   ├── server.py          # Main FastAPI application
│   ├── requirements.txt   # Python dependencies
│   └── .env              # Environment variables
├── frontend/
│   ├── src/
│   │   ├── App.js        # Main React component
│   │   ├── components/   # UI components
│   │   └── hooks/        # Custom React hooks
│   ├── package.json      # Node.js dependencies
│   └── .env             # Frontend environment variables
└── README.md
```

### Key Dependencies

#### Backend
- `fastapi` - Modern web framework
- `motor` - Async MongoDB driver
- `emergentintegrations` - AI integration library
- `requests` - HTTP client for news APIs
- `pydantic` - Data validation

#### Frontend
- `react` - UI framework
- `recharts` - Chart library
- `axios` - HTTP client
- `@radix-ui/*` - UI component library
- `tailwindcss` - CSS framework

## 🔍 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running locally
   - Check the `MONGO_URL` in `backend/.env`
   - For MongoDB Atlas, ensure IP whitelist is configured

2. **API Key Errors**
   - Verify all API keys are valid and active
   - Check rate limits for free tier APIs
   - Ensure `.env` file is in the correct location

3. **CORS Issues**
   - Verify `REACT_APP_BACKEND_URL` in `frontend/.env`
   - Check that backend is running on the correct port

4. **News Fetching Fails**
   - Check API key validity
   - Verify internet connection
   - Check API rate limits

### Testing the Setup

1. **Backend Health Check**:
   ```bash
   curl http://localhost:8000/api/status
   ```

2. **Frontend Connection**:
   - Open browser to `http://localhost:3000`
   - Check browser console for errors

## 📈 Performance Considerations

- **Rate Limits**: Be aware of API rate limits for news sources
- **Database**: Consider indexing for better query performance
- **Caching**: Implement caching for frequently accessed data
- **Error Handling**: Robust error handling for external API failures

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the API documentation
3. Check logs for error messages
4. Create an issue in the repository

---

**Note**: This application is for educational and research purposes. Always verify sentiment analysis results with additional research before making trading decisions.