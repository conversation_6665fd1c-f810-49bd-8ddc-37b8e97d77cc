# Copy this file to .env and fill in your actual values

# Gemini AI API Key (required)
GEMINI_API_KEY=your_gemini_api_key_here

# MongoDB Configuration (for local development)
MONGO_URL=*****************************************************************************
DB_NAME=market_sentiment

# For production deployment
# MONGO_URL=mongodb+srv://username:<EMAIL>/market_sentiment
# DB_NAME=market_sentiment

# Google Cloud Project (for deployment)
GCP_PROJECT_ID=your_project_id_here
GCP_REGION=us-central1
