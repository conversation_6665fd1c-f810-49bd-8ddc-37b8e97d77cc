name: Test and Build

on:
  pull_request:
    branches: [ main, master ]
  push:
    branches-ignore: [ main, master ]

jobs:
  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt

    - name: Test backend build
      run: |
        cd backend
        python -c "import server; print('Backend imports successfully')"

  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'yarn'
        cache-dependency-path: frontend/yarn.lock

    - name: Install frontend dependencies
      run: |
        cd frontend
        yarn install --frozen-lockfile

    - name: Build frontend
      run: |
        cd frontend
        REACT_APP_API_URL=http://localhost:8000 yarn build

    - name: Test frontend build
      run: |
        cd frontend
        test -d build && echo "Frontend build successful"

  docker-build:
    name: Test Docker Builds
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Test Backend Docker Build
      run: |
        docker build -t test-backend ./backend

    - name: Test Frontend Docker Build
      run: |
        docker build -t test-frontend \
          --build-arg REACT_APP_API_URL=http://localhost:8000 \
          ./frontend
