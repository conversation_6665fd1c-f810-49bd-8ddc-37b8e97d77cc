name: Deploy to Google Cloud Run

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-central1
  BACKEND_SERVICE: market-sentiment-backend
  FRONTEND_SERVICE: market-sentiment-frontend

jobs:
  deploy:
    name: Deploy to Cloud Run
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}

    - name: Configure Docker for GCR
      run: gcloud auth configure-docker

    - name: Build Backend Image
      run: |
        docker build -t gcr.io/$PROJECT_ID/$BACKEND_SERVICE:$GITHUB_SHA ./backend
        docker build -t gcr.io/$PROJECT_ID/$BACKEND_SERVICE:latest ./backend

    - name: Push Backend Image
      run: |
        docker push gcr.io/$PROJECT_ID/$BACKEND_SERVICE:$GITHUB_SHA
        docker push gcr.io/$PROJECT_ID/$BACKEND_SERVICE:latest

    - name: Deploy Backend to Cloud Run
      run: |
        gcloud run deploy $BACKEND_SERVICE \
          --image gcr.io/$PROJECT_ID/$BACKEND_SERVICE:$GITHUB_SHA \
          --platform managed \
          --region $REGION \
          --allow-unauthenticated \
          --port 8000 \
          --memory 1Gi \
          --cpu 1 \
          --set-env-vars="DB_NAME=market_sentiment" \
          --set-secrets="GEMINI_API_KEY=gemini-api-key:latest,MONGO_URL=mongo-connection-string:latest"

    - name: Get Backend URL
      id: backend-url
      run: |
        BACKEND_URL=$(gcloud run services describe $BACKEND_SERVICE --region=$REGION --format="value(status.url)")
        echo "url=$BACKEND_URL" >> $GITHUB_OUTPUT

    - name: Build Frontend Image
      run: |
        docker build -t gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:$GITHUB_SHA \
          --build-arg REACT_APP_API_URL=${{ steps.backend-url.outputs.url }} \
          ./frontend
        docker build -t gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:latest \
          --build-arg REACT_APP_API_URL=${{ steps.backend-url.outputs.url }} \
          ./frontend

    - name: Push Frontend Image
      run: |
        docker push gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:$GITHUB_SHA
        docker push gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:latest

    - name: Deploy Frontend to Cloud Run
      run: |
        gcloud run deploy $FRONTEND_SERVICE \
          --image gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:$GITHUB_SHA \
          --platform managed \
          --region $REGION \
          --allow-unauthenticated \
          --port 80 \
          --memory 512Mi \
          --cpu 1

    - name: Show Deployment URLs
      run: |
        echo "Backend URL: ${{ steps.backend-url.outputs.url }}"
        echo "Frontend URL: $(gcloud run services describe $FRONTEND_SERVICE --region=$REGION --format='value(status.url)')"
