from fastapi import FastAP<PERSON>, API<PERSON><PERSON><PERSON>, HTTPException
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timezone
import asyncio
import google.generativeai as genai

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection with fallback
try:
    mongo_url = os.environ['MONGO_URL']
    client = AsyncIOMotorClient(mongo_url, serverSelectionTimeoutMS=5000)
    db = client[os.environ['DB_NAME']]
    USE_DATABASE = True
    logging.info("MongoDB client initialized")
except Exception as e:
    logging.warning(f"MongoDB initialization failed: {e}. Using in-memory storage.")
    USE_DATABASE = False
    client = None
    db = None

# In-memory storage as fallback
memory_storage = {
    "sentiment_analyses": []
}

# API Keys
GEMINI_API_KEY = os.environ['GEMINI_API_KEY']

# Configure Gemini
genai.configure(api_key=GEMINI_API_KEY)

# Create the main app without a prefix
app = FastAPI()

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Pydantic Models
class NewsImpact(BaseModel):
    headline: str
    impact_score: float  # 0-10 how much this news affected the sentiment
    sentiment_direction: str  # "bullish", "bearish", "neutral"
    source: str  # news source or category
    relevance: str  # "high", "medium", "low"

class SentimentAnalysis(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    overall_sentiment: str  # "bullish", "bearish", "neutral"
    bullish_score: float  # 0-10
    bearish_score: float  # 0-10
    confidence_score: float  # 0-1
    analysis_summary: str
    detailed_analysis: str  # New field for comprehensive AI analysis
    influential_news: List[NewsImpact] = Field(default_factory=list)  # News that influenced the decision
    market_drivers: List[str] = Field(default_factory=list)  # Key factors driving the sentiment

# Helper Functions

async def analyze_market_sentiment_with_gemini():
    """Analyze current market sentiment using Gemini 2.5 Pro with real-time data access"""
    try:
        system_message = """You are a professional forex market analyst with access to real-time financial data and news.
Analyze the current latest forex market sentiment based on the latest available information including:
- Recent central bank decisions and monetary policy changes
- Economic indicators (GDP, inflation, employment data)
- Geopolitical events affecting major currencies
- Market trends and technical analysis
- Global economic outlook

Provide your analysis in this exact JSON format:
{
  "overall_sentiment": "bullish|bearish|neutral",
  "bullish_score": 0-10,
  "bearish_score": 0-10,
  "confidence_score": 0.0-1.0,
  "analysis_summary": "Brief 2-3 sentence summary of key factors",
  "detailed_analysis": "Comprehensive analysis covering major currency pairs, key economic events, and market outlook",
  "influential_news": [
    {
      "headline": "News headline that influenced your decision",
      "impact_score": 0-10,
      "sentiment_direction": "bullish|bearish|neutral",
      "source": "Central Bank|Economic Data|Geopolitical|Market News",
      "relevance": "high|medium|low"
    }
  ],
  "market_drivers": ["Key factor 1", "Key factor 2", "Key factor 3"]
}

Rules:
- bullish_score: How likely the overall forex market will trend upward (0-10)
- bearish_score: How likely the overall forex market will trend downward (0-10)
- confidence_score: How confident you are in this analysis (0.0-1.0)
- overall_sentiment: The dominant sentiment based on current market conditions
- analysis_summary: Brief explanation of the most important factors
- detailed_analysis: In-depth analysis of current market conditions, major currency pairs (USD, EUR, GBP, JPY, etc.), recent economic events, and outlook
- influential_news: Array of 3-7 specific news headlines/events that most influenced your sentiment decision, with their impact scores
- market_drivers: Array of 3-5 key factors currently driving market sentiment

IMPORTANT: For influential_news, provide actual recent news headlines or events that would realistically affect forex markets. Be specific about central bank announcements, economic data releases, geopolitical events, etc. Each news item should have a clear impact on your sentiment analysis.

Focus on major forex pairs and provide actionable insights for traders."""

        # Initialize Gemini model
        model = genai.GenerativeModel('gemini-2.5-pro')

        prompt = f"{system_message}\n\nAnalyze the current forex market sentiment based on the latest available financial data and news:"
        response = model.generate_content(prompt)

        # Parse the JSON response
        import json
        try:
            response_text = response.text.strip().replace("```json", "").replace("```", "")
            analysis_data = json.loads(response_text)

            # Parse influential news
            influential_news = []
            for news_item in analysis_data.get("influential_news", []):
                influential_news.append(NewsImpact(
                    headline=news_item["headline"],
                    impact_score=float(news_item["impact_score"]),
                    sentiment_direction=news_item["sentiment_direction"],
                    source=news_item["source"],
                    relevance=news_item["relevance"]
                ))

            return SentimentAnalysis(
                overall_sentiment=analysis_data["overall_sentiment"],
                bullish_score=float(analysis_data["bullish_score"]),
                bearish_score=float(analysis_data["bearish_score"]),
                confidence_score=float(analysis_data["confidence_score"]),
                analysis_summary=analysis_data["analysis_summary"],
                detailed_analysis=analysis_data["detailed_analysis"],
                influential_news=influential_news,
                market_drivers=analysis_data.get("market_drivers", [])
            )
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            return SentimentAnalysis(
                overall_sentiment="neutral",
                bullish_score=5.0,
                bearish_score=5.0,
                confidence_score=0.5,
                analysis_summary="Analysis completed but response parsing failed.",
                detailed_analysis="Unable to parse the detailed analysis from the AI response.",
                influential_news=[],
                market_drivers=["Technical error occurred"]
            )

    except Exception as e:
        logging.error(f"Error analyzing sentiment with Gemini: {e}")
        return SentimentAnalysis(
            overall_sentiment="neutral",
            bullish_score=5.0,
            bearish_score=5.0,
            confidence_score=0.3,
            analysis_summary=f"Analysis failed due to technical error: {str(e)[:100]}...",
            detailed_analysis="Technical error occurred during analysis. Please try again later.",
            influential_news=[],
            market_drivers=["Technical error occurred"]
        )

# API Endpoints
@api_router.get("/")
async def root():
    return {"message": "Forex Sentiment Analysis API", "status": "active"}

@api_router.post("/analyze")
async def analyze_market_sentiment():
    """Analyze current market sentiment using AI"""
    try:
        # Analyze sentiment using Gemini with real-time data access
        sentiment_analysis = await analyze_market_sentiment_with_gemini()

        # Store in database or memory
        try:
            if USE_DATABASE and client:
                await db.sentiment_analyses.insert_one(sentiment_analysis.model_dump())
            else:
                # Store in memory
                memory_storage["sentiment_analyses"].append(sentiment_analysis.model_dump())
        except Exception as e:
            logging.warning(f"Database storage failed, using memory: {e}")
            # Fallback to memory storage
            memory_storage["sentiment_analyses"].append(sentiment_analysis.model_dump())

        return {
            "sentiment_analysis": sentiment_analysis
        }

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in analyze_market_sentiment: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@api_router.get("/sentiment-history")
async def get_sentiment_history(limit: int = 50):
    """Get historical sentiment analysis data for charts"""
    try:
        try:
            if USE_DATABASE and client:
                cursor = db.sentiment_analyses.find().sort("timestamp", -1).limit(limit)
                analyses = await cursor.to_list(length=limit)
                # Reverse to get chronological order
                analyses.reverse()
            else:
                # Use memory storage
                analyses = memory_storage["sentiment_analyses"][-limit:]
        except Exception as e:
            logging.warning(f"Database query failed, using memory: {e}")
            # Fallback to memory storage
            analyses = memory_storage["sentiment_analyses"][-limit:]
        
        return {
            "analyses": [
                {
                    "id": analysis["id"],
                    "timestamp": analysis["timestamp"].isoformat(),
                    "overall_sentiment": analysis["overall_sentiment"],
                    "bullish_score": analysis["bullish_score"],
                    "bearish_score": analysis["bearish_score"],
                    "confidence_score": analysis["confidence_score"],
                    "analysis_summary": analysis["analysis_summary"],
                    "detailed_analysis": analysis.get("detailed_analysis", ""),
                    "influential_news": analysis.get("influential_news", []),
                    "market_drivers": analysis.get("market_drivers", [])
                }
                for analysis in analyses
            ]
        }
    except Exception as e:
        logging.error(f"Error fetching sentiment history: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch sentiment history")

@api_router.get("/status")
async def get_api_status():
    """Check API status and connectivity"""
    status = {
        "database": "disconnected",
        "gemini": "unknown"
    }

    try:
        # Test database
        if USE_DATABASE and client:
            # Quick ping test with short timeout
            await client.admin.command('ping')
            status["database"] = "connected"
        else:
            status["database"] = "memory_fallback"
    except:
        status["database"] = "disconnected"

    # Test Gemini
    try:
        model = genai.GenerativeModel('gemini-2.0-flash')
        test_response = model.generate_content("Say 'test' and nothing else.")
        if test_response and test_response.text:
            status["gemini"] = "connected"
        else:
            status["gemini"] = "disconnected"
    except:
        status["gemini"] = "disconnected"

    return status

# Include the router in the main app
app.include_router(api_router)

# CORS configuration for production
allowed_origins = [
    "http://localhost:3000",  # Local development
    "https://market-sentiment-frontend-yjs4lg3v6q-uc.a.run.app",  # Production frontend
    "*"  # Allow all for now, can be restricted later
]

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=allowed_origins,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Shutdown handler
async def shutdown_db_client():
    if USE_DATABASE and client:
        client.close()